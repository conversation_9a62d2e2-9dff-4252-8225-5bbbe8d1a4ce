{"Common": {"contactUs": "Susisiekite", "getStarted": "<PERSON><PERSON><PERSON><PERSON>", "learnMore": "Suž<PERSON>ti daugiau", "bookMeeting": "Rezervuoti susitikimą", "viewPricing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ourServices": "Mūsų paslaugos", "talkToHuman": "Kalbėti su žmogumi"}, "Navigation": {"home": "Pradžia", "about": "Apie mus", "services": "Paslaugos", "testimonials": "Atsiliepimai", "faq": "DUK", "contact": "Kontaktai", "approach": "<PERSON><PERSON><PERSON>", "websiteDevelopment": "Svetainių kūrimas", "chatbotIntegration": "Chatbot integracija", "menu": "<PERSON><PERSON>", "menuSubtitle": "Pradėkime aptardami j<PERSON> tikslus, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir pageidavimus.", "conceptIdeation": "Koncepcija ir idėjos", "conceptDescription": "<PERSON>s s<PERSON> ir kuriame detalų jū<PERSON>ų svetainė<PERSON>.", "projectKickoff": "Projekto pradžia", "freeConsultation": "Nemokama konsultacija", "websiteDevelopmentDesc": "<PERSON><PERSON>, sukurtos naudojant šiuolaikines technologijas", "chatbotIntegrationDesc": "Dirbtinio intelekto chatbotai klientų įtraukimui"}, "Footer": {"references": "<PERSON><PERSON><PERSON><PERSON>", "services": "Paslaugos", "socialNetworks": "Socialiniai tinklai", "contact": "Kontaktai", "approach": "<PERSON><PERSON><PERSON>", "aboutUs": "Apie mus", "testimonials": "Atsiliepimai", "faq": "DUK", "contactUs": "Kontaktai", "websiteDevelopment": "Svetainių kūrimas", "chatbotIntegration": "Chatbot integracija", "linkedin": "LinkedIn", "newsletter": "Prenumeruokite mūsų naujienlaiškį", "copyright": "© 2025 UpZera. Visos teisės saugomos."}, "Chatbot": {"viewPricing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "viewPricingDesc": "Peržiūrėkite mūsų kainas ir paketus", "bookMeeting": "Rezervuoti susitikimą", "bookMeetingDesc": "Suplanuokite nemokamą konsultaciją", "ourServices": "Mūsų paslaugos", "ourServicesDesc": "Sužinokite apie mūsų sprendimus", "talkToHuman": "Kalbėti su žmogumi", "talkToHumanDesc": "Gaukite individualų pagalbą", "welcomeMessage": "Sveiki! 👋 Aš esu UpZera asistentas.", "welcomeDescription": "<PERSON><PERSON> kuriame protingus skaitmeninio sprendi<PERSON>, kurie stumia verslą į priekį. A<PERSON> ką norėtum<PERSON> su<PERSON>?", "onlineStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offlineStatus": "Neprisijungęs", "replyPlaceholder": "Atsakyti UpBot", "quickActionsTitle": "Apie ką norėtumėte sužinoti?", "quickActionServices": "🚀 <PERSON><PERSON><PERSON><PERSON> paslaugos", "quickActionPricing": "💰 Kainos", "quickActionBookMeeting": "📅 Rezervuoti susitikimą", "quickActionPortfolio": "💼 Portfe<PERSON>", "quickActionServicesMessage": "Papasakokite apie savo paslaugas", "quickActionPricingMessage": "<PERSON><PERSON>os jū<PERSON> kaino<PERSON>?", "quickActionConsultationMessage": "<PERSON>iu rezervuoti konsultaciją", "quickActionPortfolioMessage": "Parodykite savo ankstesnius darbus", "serviceOptionsTitle": "Pasirinkite paslaugą, apie kurią norite sužinoti daugiau:", "serviceWebDevelopment": "Pilno ciklo s<PERSON>ain<PERSON> kūrimas", "serviceChatbots": "Chatbot potencialių klientų pritraukimas", "serviceWebDevelopmentMessage": "Papasakokite apie savo svetainių kūrimo paslaugas", "serviceChatbotsMessage": "<PERSON><PERSON> apie jūsų chatbot paslaugas", "leadFormTitle": "Kontaktinė informacija", "leadFormName": "Vardas", "leadFormEmail": "El. <PERSON>", "leadFormSubmit": "Pat<PERSON><PERSON><PERSON>", "leadFormSubmitting": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "leadFormErrorEmail": "Prašome įvesti teisingą el. pašto adresą", "leadFormErrorName": "Prašome įvesti savo vardą", "leadFormErrorSubmit": "Nepavyko pateikti. Prašome bandyti vėliau.", "leadFormSuccess": "<PERSON><PERSON><PERSON><PERSON> {name}! Mūsų komanda netrukus su jumis susisieks.", "bookingMessage": "Puiku! Mielai padėsiu jums suplanuoti konsultaciją. Leiskite parodyti mūsų rezervavimo kalendorių:", "servicesMessage": "Puiku! Štai mūsų pagrindinės paslaugos. Spustelėkite bet kurią paslaugą, kad su<PERSON>te daugiau:", "supportTicketStart": "Mielai padėsiu jums susisiekti su mūsų komanda! 🤝\n\nPadėsiu sukurti pagalbos užklausą, kad mūsų komanda galėtų suteikti individualų pagalbą.\n\nPirmiausia, ar galėtumėte pasakyti savo **vardą**?", "supportTicketNamePrompt": "Puiku! <PERSON><PERSON>, ar gal<PERSON>te pateikti savo **el. pa<PERSON><PERSON>**?", "supportTicketEmailPrompt": "Puiku! Dabar aprašykite problemą ar klausim<PERSON>, su kuriuo nor<PERSON>, kad mūsų komanda padėtų:", "supportTicketConfirm": "Ačiū! Leiskite patvirtinti jūsų pagalbos užklausos duomenis:\n\n**Vardas:** {name}\n**El. pa<PERSON>:** {email}\n**Problema:** {description}\n\nAr turiu sukurti šią pagalbos užklausą?", "supportTicketSuccess": "Puiku! 🎉 Jūsų pagalbos užklausa sėkmingai sukurta.\n\n**Užklausos duomenys:**\n• **Vardas:** {name}\n• **El. pa<PERSON>:** {email}\n• **Problema:** {description}\n\n📧 **Kiti <PERSON>:**\n• Mūsų komanda peržiūrės jūsų užklausą\n• Atsakymą gausite per 24 valandas\n• Susisieksime nurodytu el. pašto adresu\n\nA<PERSON>, kad kreip<PERSON>s į UpZera!", "supportTicketError": "<PERSON><PERSON><PERSON><PERSON><PERSON>, bet kilo problema kuriant jūsų pagalbos užklausą. Prašome bandyti dar kartą arba <NAME_EMAIL>.", "supportTicketRestart": "Gerai! Ar norėtumėte pradėti iš naujo su pagalbos užklausos informacija?", "supportTicketRestartConfirm": "Puiku! Pradėkime iš naujo. Ar <PERSON> pasakyti savo **vardą**?", "perfectConfirmDetails": "Puiku! ✅ Leiskite patvirtinti jūsų duomenis:", "nameLabel": "Vardas:", "emailLabel": "El. p<PERSON>:", "isInformationCorrect": "Ar ši informacija teisinga?", "perfectProvideDescription": "Puiku! 📧 Dabar prašome pateikti **išsamų aprašymą** problemos ar klausimo, su kuriuo susiduriate.", "tipMoreDetails": "💡 **Patarimas:** Kuo daugiau detalių pateiksite, tuo geriau gal<PERSON>sime jums padėti!", "thankYouDetailedDescription": "Ač<PERSON>ū už išsamų aprašymą! 📝", "confirmSupportTicketDetails": "Leiskite patvirtinti jūsų palaikymo užklausos duomenis:", "problemLabel": "Problema:", "shouldCreateSupportTicket": "Ar turiu sukurti palaikymo užklausą su šia informacija?", "supportTicketCreatedSuccess": "Puiku! ✅ Sukūriau jums palaikymo užklausą **#{ticketNumber}**.", "nextStepsTitle": "🔍 **<PERSON><PERSON>:**", "teamWillReview": "• Mūsų komanda peržiūrės jūsų užklausą", "responseWithin24Hours": "• Atsakysime per 24 valandas", "checkEmailForUpdates": "• Tikrinkite el. paštą dėl atnaujinimų", "thankYouForContacting": "<PERSON><PERSON><PERSON><PERSON>, kad <PERSON> į UpZera!", "confirmDetailsPrompt": "Puiku! 🎉 Sujungiu jus su mūsų komanda.\n\n📞 **Kiti <PERSON>i:**\n• Kas nors netrukus su jumis susisieks\n• Susisieksime nurodytu el. pa<PERSON>to ad<PERSON>, kad pasirinkote UpZera!", "reenterDetailsPrompt": "Gerai! Ar <PERSON> iš naujo įvesti savo duomenis?", "invalidEmailMessage": "Tai nepanašu į teisingą el. pašto adres<PERSON>. <PERSON>r gal<PERSON>ėte pateikti teisingą el. paštą?", "pleaseConfirmMessage": "<PERSON><PERSON><PERSON><PERSON> at<PERSON> 'taip', jei informacija <PERSON>, arb<PERSON> 'ne', jei norite keist<PERSON>.", "pleaseConfirmReenterMessage": "<PERSON><PERSON><PERSON><PERSON> at<PERSON> 'taip', jei norite iš naujo įvesti duomenis, arba 'ne', jei norite daryti ką nor<PERSON> kita.", "pleaseConfirmTicketMessage": "<PERSON><PERSON><PERSON><PERSON> atsa<PERSON>ti 'taip', kad sukurtum<PERSON>te pagalbos užklaus<PERSON>, arba 'ne', jei norite keist<PERSON>.", "pleaseConfirmRestartMessage": "<PERSON><PERSON><PERSON><PERSON> at<PERSON> 'taip', jei norite prad<PERSON> i<PERSON>, arba 'ne', jei norite daryti k<PERSON> nor<PERSON> kita.", "endConversationConfirm": "<PERSON><PERSON><PERSON><PERSON>, kad norite baigti pokalbį. Ar tikrai jums nereikia jokios kitos pagalbos šiandien?", "endConversationGoodbye": "Buvo malonu su jumis kalb<PERSON>! Drąsiai kreipkitės bet kada, jei tur<PERSON>site daugiau klausimų. Geros dienos! 👋", "continueConversation": "Puiku! <PERSON><PERSON> čia, kad <PERSON>. A<PERSON> ką norėtumėte sužinoti daugiau?", "anythingElseYes": "Puiku! Apie ką dar norėtumė<PERSON> sužinoti?", "anythingElseNo": "Suprantu! <PERSON><PERSON><PERSON> bai<PERSON> pokalbį, ar tikrai jums nereikia jokios kitos pagalbos šiandien? 🤔", "helpMenuResponses": ["<PERSON><PERSON>i padėsiu jums! <PERSON><PERSON> pagrindin<PERSON>i būdai, ka<PERSON> galiu pad<PERSON>:", "Padėkime rasti, ko ieškote! Š<PERSON> pagrindinė<PERSON> pas<PERSON>, su kuriomis galiu pad<PERSON>:", "<PERSON><PERSON>, kad pad<PERSON>! Štai pagrindinės s<PERSON>, kuriose galiu suteikti pagalbą:", "Puikus klausimas! Leiskite parodyti pagrin<PERSON> bū<PERSON>, ka<PERSON> galiu pad<PERSON>:", "Mielai pad<PERSON>u! <PERSON><PERSON> pagrindinės s<PERSON>, kuriose gal<PERSON> pad<PERSON>:", "Jokių problemų! Štai pagrindinės paslau<PERSON>, su kuriomis galiu pad<PERSON>:", "Esu pasiruoš<PERSON> padėti! Štai pagrindiniai būdai, kaip galiu padėti:", "Leiskite nukreipti jus į tinkamą vietą! Štai pagrindiniai variantai:"], "followUpPricing": "<PERSON><PERSON> <PERSON><PERSON><PERSON>te sužinoti daugiau apie konkrečių paslaugų kainas?", "followUpServices": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> sužinoti daugiau apie mūsų kitas paslaugas?", "bookingYesResponse": "Puiku! Norėdami rezervuoti susitikimą, prašome nurodyti pageidaujamą datą ir laiką, arba galite patikrinti mūsų rezervavimo kalendorių dėl galimybių.", "aiErrorMessage": "Šiuo metu turiu problemų su ryšiu. Pabandykite paklausti apie mūsų paslaugas arba rezervuokite nemokamą konsultaciją!", "aiErrorFallback": "Leiskite sujungti jus su mūsų komanda! Galite rezervuoti nemokamą konsultaciją arba rašyti mums el. paštu <EMAIL>.", "fallbackHelpMessage": "<PERSON><PERSON>i padėsiu jums! Leiskite parodyti, su kuo galiu padėti:", "restartConversation": "Pradėti naują pokalbį", "conversationEnded": "Pokalbis baigtas", "mainMenuPricesMessage": "<PERSON><PERSON>ti apie jūsų kainas", "mainMenuBookingMessage": "Noriu rezervuoti susitikimą", "mainMenuServicesMessage": "Papasakokite apie savo paslaugas", "mainMenuHumanMessage": "Reikia kalbėti su žmogumi", "mainMenuGreeting": "<PERSON><PERSON> galiu jums <PERSON>dien pad<PERSON>?", "wouldYouLikeToKnowMoreServices": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> sužinoti daugiau apie mūsų kitas paslaugas?", "wouldYouLikeToKnowMorePricing": "<PERSON><PERSON> <PERSON><PERSON><PERSON>te sužinoti daugiau apie konkrečių paslaugų kainas?", "greatToBookMeeting": "Puiku! Norėdami užsisakyti susitikimą, prašome nurodyti pageidaujamą datą ir laiką arba galite patikrinti mūsų rezervacijos kalendorių.", "makeSureUnderstandCorrectly": "<PERSON><PERSON>, kad te<PERSON>ai suprantu. Ar jums reikia dar kokios nors pagalbos šiandien, ar esate pasirengę baigti pokalbį?", "happyToHelpMainWays": "Mielai jums padėsiu! Štai pagrindiniai būdai, kaip galiu jums padėti:", "letMeHelpFindServices": "Leiskite padėti jums rasti tai, ko ieškote! <PERSON><PERSON> pagrin<PERSON> p<PERSON>, su kuriomis galiu padėti:", "hereToHelpMainAreas": "<PERSON><PERSON>, kad pad<PERSON>! Štai pagrindinės s<PERSON>, kuriose galiu suteikti pagalbą:", "greatQuestionMainWays": "Puikus klausimas! Leiskite parodyti pagrin<PERSON> bū<PERSON>, ka<PERSON> galiu pad<PERSON>:", "loveToAssistKeyAreas": "Mielai pad<PERSON>u! <PERSON><PERSON> pagrindinės s<PERSON>, kuriose gal<PERSON> pad<PERSON>:", "noProblemMainServices": "Jokių problemų! Štai pagrindinės paslau<PERSON>, su kuriomis galiu pad<PERSON>:", "readyToHelpPrimaryWays": "Esu pasirengęs padėti! Štai pagrindiniai būdai, kaip galiu jums padėti:", "letMeGuideMainOptions": "Leiskite nukreipti jus į tinkamą vietą! Štai pagrindiniai variantai:", "understandEndConversation": "<PERSON><PERSON><PERSON><PERSON>, kad norite baigti pokalbį. Ar tikrai jums nereikia jokios kitos pagalbos šiandien?", "happyToHelpShowAssist": "<PERSON><PERSON>i padėsiu jums! Leiskite parodyti, su kuo galiu padėti:", "greatWhatElseKnow": "Puiku! Apie ką dar norėtumė<PERSON> sužinoti?", "understandBeforeEndChat": "Suprantu! <PERSON><PERSON><PERSON> bai<PERSON> pokalbį, ar tikrai jums nereikia jokios kitos pagalbos šiandien? 🤔", "invalidEmailAddress": "Tai nepanašu į teisingą el. pašto adres<PERSON>. <PERSON>r gal<PERSON>ėte pateikti teisingą el. paštą?", "greatConnectingTeam": "Puiku! 🎉 Sujungiu jus su mūsų komanda.\n\n📞 **Kiti <PERSON>i:**\n• Kas nors netrukus su jumis susisieks\n• Susisieksime nurodytu el. pa<PERSON>to ad<PERSON>, kad pasirinkote UpZera!", "noProblemReenterDetails": "Gerai! Ar <PERSON> iš naujo įvesti savo duomenis?", "pleaseAnswerYesNoCorrect": "<PERSON><PERSON><PERSON><PERSON> at<PERSON> 'taip', jei informacija <PERSON>, arb<PERSON> 'ne', jei norite keist<PERSON>.", "pleaseAnswerYesNoReenter": "<PERSON><PERSON><PERSON><PERSON> at<PERSON> 'taip', jei norite iš naujo įvesti duomenis, arba 'ne', jei norite daryti ką nor<PERSON> kita.", "apologizeIssueCreatingTicket": "<PERSON><PERSON><PERSON><PERSON><PERSON>, bet kilo problema kuriant jūsų pagalbos užklausą. Prašome bandyti dar kartą arba <NAME_EMAIL>.", "noProblemStartOverTicket": "Gerai! Ar norėtumėte pradėti iš naujo su pagalbos užklausos informacija?", "pleaseAnswerYesNoCreateTicket": "<PERSON><PERSON><PERSON><PERSON> atsa<PERSON>ti 'taip', kad sukurtum<PERSON>te pagalbos užklaus<PERSON>, arba 'ne', jei norite keist<PERSON>.", "greatStartOverName": "Puiku! Pradėkime iš naujo. Ar <PERSON> pasakyti savo **vardą**?", "pleaseAnswerYesNoStartOver": "<PERSON><PERSON><PERSON><PERSON> at<PERSON> 'taip', jei norite prad<PERSON> i<PERSON>, arba 'ne', jei norite daryti k<PERSON> nor<PERSON> kita.", "pleasureToChat": "Buvo malonu su jumis kalb<PERSON>! Drąsiai kreipkitės bet kada, jei tur<PERSON>site daugiau klausimų. Geros dienos! 👋", "perfectHereToHelp": "Puiku! <PERSON><PERSON> čia, kad <PERSON>. A<PERSON> ką norėtumėte sužinoti daugiau?", "letUsKnowAnythingElse": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jei re<PERSON> dar ko nors! 😊", "areYouStillThere": "Ar dar esate čia? <PERSON>su čia, jei reikia pagal<PERSON>! 😊", "beenAwayForAWhile": "<PERSON><PERSON><PERSON>, kad jūs buvote išvykę kurį laiką. Drąsiai pradėkite naują pokalbį bet kada, kai reikės pagal<PERSON>! 👋", "noProblemAnythingElse": "Jokių problemų! Ar yra dar ko nors, su kuo galiu pad<PERSON>?", "gotIt": "Supratau!", "happyToHelpLearnServices": "Mielai padėsiu jums sužinoti daugiau apie mūsų paslaugas.", "anythingElseToday": "Ar yra dar ko nors, su kuo galiu pad<PERSON>die<PERSON>?", "sureStartOverName": "Žinoma! Pradėkime iš naujo. Koks jū<PERSON> vardas?", "noWorriesAnythingElse": "Nesijaudinkite! Ar yra dar ko nors, su kuo galiu pad<PERSON>?", "noProblemAnythingElseToday": "Jokių problemų! Ar yra dar ko nors, su kuo galiu pad<PERSON> š<PERSON>dien?", "chatHasEnded": "Pokalbis baigtas", "startNewChat": "Pradėti naują pokalbį", "niceToMeetYouEmailPrompt": "<PERSON><PERSON><PERSON>, {name}! <PERSON><PERSON>, ar <PERSON>te pateikti savo el. pašto ad<PERSON>?", "thankYouEmailPrompt": "<PERSON><PERSON><PERSON><PERSON>, {name}! <PERSON><PERSON>, ar <PERSON>te pateikti savo el. pa<PERSON>to ad<PERSON>?", "fallbackGreeting": "Sveiki! Kaip galiu jums pad<PERSON>ti š<PERSON>dien?"}, "FAQ": {"title": "Dažnai užduodami klausimai", "description": "Raskite atsakymus į da<PERSON><PERSON><PERSON>ius klausimus apie mūsų paslaugas ir metodus", "q1": "Su kokiais verslais dirbate?", "a1": "Papra<PERSON>i dir<PERSON> su <PERSON>, <PERSON><PERSON><PERSON><PERSON> k<PERSON> ir auganč<PERSON> vers<PERSON>, kuri<PERSON> reikia greitų, patikimų skaitmeninių įrankių — nuo nusileidimo puslapių iki sudėtingesnių dizainų. <PERSON><PERSON><PERSON><PERSON>, ar tik <PERSON>, ar <PERSON><PERSON><PERSON><PERSON><PERSON>, me<PERSON>, kad <PERSON>.", "q2": "<PERSON>ek kainuoja jūsų paslaugos?", "a2": "Gaus<PERSON>, individualizuotą pasiūlymą po nemokamos konsultacijos.<br />Mūsų kainodara yra pagrįsta projektu ir priklauso nuo jūsų konkrečių tikslų ir apimties. Dauguma projektų kainuoja nuo 350–500 € nusileidimo pusla<PERSON>ms, 1 400–2 400 € pilnoms svetainėms ir 500–1 200 € chatbot nustatymams.", "q3": "<PERSON><PERSON> greitai galite pristatyti projektą?", "a3": "Terminai skiriasi priklausomai nuo sud<PERSON>, bet dauguma nusileidimo puslapių būna paruošti per keli<PERSON> die<PERSON>, o pilni projektai paprastai užtrunka 2–4 savaites. Duosime aiškų terminą prieš pradėdami — ir jo la<PERSON>.", "q4": "Ar teikiate nuolatinę pagalbą?", "a4": "Absoliučiai — bet tik tada, kai jums to reikia. Esame čia po paleidimo d<PERSON>, atnaujinimų ar patobulinimų. Nebūsite perduoti pagalbos tarnybai — tiesiog susisiekite ir mes padėsime.", "q5": "Kuo skiriates nuo kitų svetainių kūrimo komandų?", "a5": "Mes esame inžinieriai širdyje, apsėsti švarių sprendimų. Naudojame dirbtinio intelekto darbo sraut<PERSON>, kad pristatytume greičiau neprarasdami kokybė<PERSON> — ir pritaikome viską jūsų verslui, o ne kokiam nors šablonui.", "q6": "<PERSON><PERSON>?", "a6": "Paprasta — tiesiog rezervuokite nemokamą konsultaciją. Kalbėsimės apie jūsų tikslus, įvertinsime jūsų poreikius ir duosime individualų veiksmų planą.", "q7": "<PERSON><PERSON> t<PERSON>kote sąska<PERSON> ir mokėjimus?", "a7": "<div class=\"space-y-4\"><p><PERSON>k<PERSON>ji<PERSON> darome paprastus ir skaidrius:</p><ul class=\"list-disc list-inside space-y-2 pl-4\"><li><b>Nemokamas koncepcijos peržiūra</b> - <PERSON><PERSON><PERSON><PERSON> maž<PERSON> dizaino per<PERSON>, kad įsitikintume, jog tinkame</li><li><b>Sutar<PERSON> pasirašymas</b> - <PERSON><PERSON> patinka kryptis, formalizuosime susitarimą</li><li><b>Depozi<PERSON> mok<PERSON>jima<PERSON></b> - 20% mažiems projektams arba etapais didesniems</li><li><b>Aiškios sąskaitos</b> - <PERSON>gal sutartus etapus arba galutinį pristatymą</li><li><b>Lankstūs terminai</b> - Standartiniai NET15 mokėjimo terminai, bet galime prisitaikyti prie jūsų poreikių</li></ul><p><PERSON><PERSON><PERSON> staigmen<PERSON> — tik aišk<PERSON><PERSON> mokėjimo terminai, kurie tinka mums abiem.</p></div>", "q8": "Ko reikia projektui pradėti?", "a8": "<div class=\"space-y-4\"><p>Mūsų supaprastintas 5 žingsnių procesas:</p><ol class=\"list-decimal list-inside space-y-3 pl-4\"><li class=\"font-medium\"><PERSON><PERSON><PERSON><PERSON><PERSON> skambutis<br /><span class=\"font-normal text-purple-100/80\">Aptarsime jūsų viziją ir reikalavimus</span></li><li class=\"font-medium\">Neprivaloma peržiūra<br /><span class=\"font-normal text-purple-100/80\">Nemokama koncepcijos demonstracija, kad užtikrintume suderinamumą</span></li><li class=\"font-medium\">Susitarimas ir depozitas<br /><span class=\"font-normal text-purple-100/80\">Pasirašykite sutartį ir sumokėkite depozitą</span></li><li class=\"font-medium\">Medžiagų perdavimas<br /><span class=\"font-normal text-purple-100/80\">Pasidalinkite savo ištekliais ir prieigos duomenimis</span></li><li class=\"font-medium\">Kūrimo etapas<br /><span class=\"font-normal text-purple-100/80\">Efektyviai kuriame jūsų sprendimą</span></li></ol><p>Paprasta, skaidru ir orientuota į tikslų to, ko jums reikia, pristatymą.</p></div>", "stillHaveQuestions": "Vis dar turite klausim<PERSON>?", "stillHaveQuestionsDesc": "<PERSON><PERSON>, kad pad<PERSON> su bet kokiais klausimais apie mūsų paslaugas"}, "HomePage": {"heroTitle": "<PERSON><PERSON><PERSON> s<PERSON> sprendi<PERSON>, kurie augina jū<PERSON> versl<PERSON>", "heroHighlight": "be joki<PERSON> pastang<PERSON>", "heroSubtitle": "UpZera padeda jums augti su dirbtinio intelekto sprendimais, kurie sujungia svetaines ir chatbotus į vieną sklandžią patirtį.", "launchWithUs": "Pradėkite su mumis", "freeConsultation": "Nemokama konsultacija", "fastTurnaround": "Greitas įgyvendinimas", "coreServicesTitle": "<PERSON><PERSON><PERSON><PERSON> pag<PERSON> pas<PERSON>", "fullStackTitle": "Pilno ciklo s<PERSON>ain<PERSON> kūrimas", "fullStackDesc": "Nuo dizaino iki backend log<PERSON> — kuriame visiškai funkcionalias svetaines, kurios atrodo puikiai ir veikia dar geriau.", "chatbotsTitle": "Chatbotai ir potencialių klientų pritraukimas", "chatbotsDesc": "Pritraukite potencial<PERSON> k<PERSON>, kvalifikuokite juos ir atsakykite akimirksniu su specialiai sukurtais chat<PERSON>, kurie ve<PERSON>a 24/7.", "supportTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supportDesc": "Mes nedingame po paleidimo — esame <PERSON>, kad pri<PERSON><PERSON><PERSON><PERSON><PERSON>, tobulintume ir augintume jūsų sprendimą kartu su jumis.", "deploymentTitle": "Žaibiškai greitas diegimas", "deploymentDesc": "Paleiskite savo sprendimus greitai su mūsų supaprastintu diegimo procesu.", "techStackTitle": "Mūsų", "techStackHighlight": "technologijų rink<PERSON>ys", "techStackDesc": "<PERSON><PERSON><PERSON><PERSON> technolog<PERSON>, kad sukurtume patiki<PERSON>, pleč<PERSON>us ir aukšto našumo internetinius sprendimus.", "whyChooseTitle": "<PERSON><PERSON><PERSON><PERSON> rinktis UpZera?", "otherSpecialists": "<PERSON><PERSON>", "limitedResultsTitle": "Riboti rezultatai ir aukš<PERSON> ka<PERSON>:", "limitedResultsDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON> u<PERSON> pagrin<PERSON> sp<PERSON>, daug<PERSON><PERSON> d<PERSON><PERSON>io skiria išvaizdai nei našumui/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ne<PERSON><PERSON><PERSON> p<PERSON>.", "confusingProcessTitle": "<PERSON><PERSON><PERSON> pro<PERSON>, <PERSON><PERSON><PERSON><PERSON>myb<PERSON> p<PERSON>:", "confusingProcessDesc": "Komplikuoja su žargonu, ne<PERSON><PERSON><PERSON> kas kuria produktą, l<PERSON><PERSON>.", "oneWayCommTitle": "<PERSON><PERSON><PERSON><PERSON> bend<PERSON>:", "oneWayCommDesc": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON>o <PERSON>.", "weAtUpZera": "Mes UpZera esame", "affordableTitle": "<PERSON><PERSON><PERSON><PERSON>, pritaikyti ir pilno ciklo:", "affordableDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON>, pilno c<PERSON>, pritaikyti jū<PERSON>ų poreikiams.", "clearProcessTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> pro<PERSON>, <PERSON><PERSON><PERSON><PERSON> p<PERSON>:", "clearProcessDesc": "<PERSON><PERSON><PERSON> m<PERSON>, p<PERSON><PERSON><PERSON>, a<PERSON><PERSON><PERSON><PERSON><PERSON> terminai, visada žinote statusą.", "personalTitle": "Asmeniškai ir greitai reaguojame:", "personalDesc": "Tiesioginis priėjimas prie kūr<PERSON>j<PERSON>, greitas bendravimas per pageidaujamus kanal<PERSON>, bendradarbiaujame ir esame skai<PERSON>.", "ctaTitle": "Turite idėją? Tyrinėkime ją kartu.", "ctaDesc": "Susisiekite su mumis, kad suplanuotumėte nemokamą projekto konsultaciją.", "useFreeConsultation": "Pasinaudoti nemokama konsultacija"}, "AboutPage": {"heroTitle": "Taigi... kas mes iš tikr<PERSON>j<PERSON>?", "heroSubtitle": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON> s<PERSON>besni nei p<PERSON>seliai. <PERSON><PERSON><PERSON>, a<PERSON><PERSON><PERSON><PERSON> ir did<PERSON> s<PERSON>.", "ourStoryTitle": "Mūsų istorija", "story1": "UpZera pradėjo veiklą 2025 metais su viena misija: kurti protingus skaitmeninio sprendimus, kurie iš tikrųjų stumia verslą į priekį — gre<PERSON><PERSON>, švariai ir tik<PERSON>lingai.", "story2": "<PERSON><PERSON> es<PERSON> kom<PERSON>, technologijo<PERSON> a<PERSON> k<PERSON>, kuria<PERSON> s<PERSON><PERSON> ir chatbotus. Integruodami dirbtinio intelekto darbo srautus į mūsų procesą, galime pristatyti greičiau niekada nekirpdami kamp<PERSON> — greitis ir patiki<PERSON>, vienas šalia kito.", "story3": "Mes jus nuvesime nuo idėjos iki paleidimo — ir kai mūsų reikia, esame pasiruo<PERSON> įsikišti ir palaikyti sklandų veikimą. Jokių dingimų. Jokių perdavimų kokiai nors beveidei pagalbos linijai. Tik tikri <PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON>, kai to reikia.", "story4": "Kiekvienas mūsų pristatomas sprendimas yra pritaikytas individualiai — jokių perkrautų šablonų, jokių nereikalingų dalykų. <PERSON><PERSON> protingi, <PERSON><PERSON><PERSON><PERSON> sp<PERSON>, sukurti aplink jū<PERSON><PERSON> verslą, jū<PERSON><PERSON> istoriją ir jūsų kitą didįjį žingsnį.", "tagline": "✨ Pakelkite savo verslą be jokių pastangų.", "teamTitle": "Vadovaujami smalsių inžinierių", "teamSubtitle": "(KEISIS) UpZera vadovauja du aistringi inžinerijos studentai — vienas elektros inžinerijos, kitas automobilių. Mes sujungiame techninį gilumą su dizaino jausmu, kad sukurtume patiki<PERSON>, efektyvius ir šiuolaikiškus sprendimus.", "edgarasRole": "<PERSON><PERSON><PERSON><PERSON>", "edgarasDesc": "Elektros inžinerijos studentas su aistra švariam kodui ir efektyvioms sistemoms.", "valuesTitle": "<PERSON>o mes tikime", "honestyTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "honestyDesc": "Sakome ką turime omenyje ir kuriame ką pažadame.", "responsibilityTitle": "Atsakomybė", "responsibilityDesc": "Jūsų projektas yra mūsų projektas. Rūpinamės juo taip pat r<PERSON>.", "clientCareTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "clientCareDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON>, antra kuria<PERSON>. Jūsų poreikiai visada pirmoje vietoje.", "trustworthinessTitle": "Patikimumas", "trustworthinessDesc": "<PERSON><PERSON><PERSON>, kad pasi<PERSON> yra u<PERSON>. Esame čia ilgam.", "servicesTitle": "<PERSON><PERSON><PERSON> c<PERSON>, <PERSON>as augimui", "webDevTitle": "Svetainių kūrimas", "webDesign": "Svetainių dizainas", "uiUxImplementation": "UI/UX įgyvendinimas", "backendTitle": "Backend ir automatizavimas", "backendLogic": "Backend logika", "processAutomation": "Procesų automatizavimas", "chatbotsTitle": "Chatbotai ir potencialių klientų įrankiai", "aiIntegrations": "DI integracijos", "leadGeneration": "Potencialių klientų pritraukimas", "integrationsTitle": "<PERSON><PERSON><PERSON> integra<PERSON>", "apis": "API", "calendarBooking": "Ka<PERSON><PERSON><PERSON>us/rezervavimo įrankiai", "performanceTitle": "Našumas ir SEO", "optimization": "Optimizavimas", "searchVisibility": "<PERSON><PERSON><PERSON><PERSON>", "supportTitle": "Nuolat<PERSON>ė pagalba", "maintenance": "Priežiūra", "updatesImprovements": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir pat<PERSON>i", "ctaFinalTitle": "Gana apie mus — kalbėkime apie jus", "ctaFinalDesc": "Pasiruoš<PERSON> pakelti savo skaitmeninį buvimą? Pradėkime pokalbį apie jūsų verslo tikslus.", "getFreeQuote": "<PERSON><PERSON><PERSON> pasiū<PERSON>"}, "ContactPage": {"heroTitle": "Nedvejokite susisiekti", "heroSubtitle": "<PERSON><PERSON><PERSON> visk<PERSON>, kad atsakytume į visus jūsų klausimus ir suteiktume jums mūsų paslaugas", "email": "El. <PERSON>", "phone": "Telefonas", "address": "<PERSON><PERSON><PERSON>", "contactUs": "Susisiekite su mumis", "name": "Vardas", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> vardas", "emailPlaceholder": "<EMAIL>", "service": "Paslauga", "message": "Žinutė", "messagePlaceholder": "Jūsų ž<PERSON>", "sending": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "contact": "<PERSON><PERSON><PERSON><PERSON>", "chatbotIntegration": "Chatbot integracija", "chatbotIntegrationDesc": "DI pokalbių sprendimai", "webDevelopment": "Svetainių kūrimas", "webDevelopmentDesc": "Individualūs svetainių sprendimai", "otherServices": "Kit<PERSON> p<PERSON>lau<PERSON>", "otherServicesDesc": "<PERSON><PERSON><PERSON>", "mvpVirtualAssistant": "MVP virtual<PERSON> asistentas", "mvpVirtualAssistantDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> chatbot funkcijos jū<PERSON> verslui", "customizableAiAssistant": "Pritaikomas DI asistentas", "customizableAiAssistantDesc": "Pažangūs sprendimai su giliokomis integracijomis", "websiteEssentials": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "websiteEssentialsDesc": "Pagrindinė svetainė su esminėmis funkcijomis", "smartBusinessWebsites": "<PERSON><PERSON><PERSON> ve<PERSON>", "smartBusinessWebsitesDesc": "Papildomos funkcijos augančiam verslui", "advancedWebPlatforms": "Pažangūs internetiniai sprendimai", "advancedWebPlatformsDesc": "Sudėtingi sprendimai su individualiomis funkcijomis", "selectedServices": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "nameRequired": "Vardas yra privalomas.", "emailRequired": "El. paštas yra privalomas.", "emailInvalid": "El. paštas neteisingas.", "serviceRequired": "Prašome pasirinkti bent vieną paslaugą.", "messageRequired": "Žinutė yra privaloma.", "thankYou": "Ačiū! Jūsų žinutė išsiųsta.", "orTitle": "ARBA!", "scheduleMeeting": "Su<PERSON><PERSON><PERSON><PERSON> susiti<PERSON>", "scheduleMeetingDesc": "Rezervuokite nemokamą konsultacijos skambutį su mūsų komanda"}, "ApproachPage": {"title": "Mūsų metodas", "subtitle": "<PERSON><PERSON><PERSON><PERSON>, kuriame ir palaikome jūsų projektą kiekviename žingsnyje.", "step1Title": "1. <PERSON><PERSON><PERSON><PERSON><PERSON>", "step1Description": "<PERSON><PERSON><PERSON><PERSON> trumpu poka<PERSON>, kad suprastume jūs<PERSON> tikslus, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir projekto viziją.", "step2Title": "2. Koncep<PERSON>ja ir neprivaloma demonstracija", "step2Description": "<PERSON><PERSON><PERSON><PERSON>, j<PERSON>, pat<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> demonstraciją, kad įsitikintume, jog esame vienoje bangoje prieš t<PERSON>.", "step3Title": "3. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> ir m<PERSON>", "step3Description": "Apibrėžiame projekto apimtį, terminus ir kainodarą. Susi<PERSON>ę pasiraš<PERSON> sutartį ir surenkame depozitą.", "step4Title": "4. <PERSON><PERSON><PERSON><PERSON>", "step4Description": "<PERSON> v<PERSON>, p<PERSON><PERSON><PERSON>. Suplanuojame etapus ir imam<PERSON> darbo.", "step5Title": "5. <PERSON><PERSON><PERSON><PERSON> ir grįžtamasis r<PERSON>", "step5Description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> ir renkame jū<PERSON><PERSON> at<PERSON>, kad viskas būtų tinkamoje vėžėje ir tikslinga.", "step6Title": "6. <PERSON><PERSON><PERSON><PERSON><PERSON> ir p<PERSON>", "step6Description": "Užbaigiame ir perduodame jūsų projektą, siū<PERSON><PERSON> pala<PERSON> ir liiekame prieinami ateities bendradarbiavimui."}, "Newsletter": {"emailPlaceholder": "Jūsų el. pašto adresas", "emailRequired": "El. paštas yra privalomas", "emailInvalid": "Prašome įvesti teisingą el. pašto adresą", "successMessage": "<PERSON><PERSON><PERSON><PERSON>, kad prenumeruojate mūsų naujienlaiškį!"}, "TestimonialsPage": {"heroTitle": "<PERSON><PERSON><PERSON> sėkmės istorijos", "heroSubtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka<PERSON> pad<PERSON> verslams transformuoti savo skaitmeninį buvimą ir pasiekti tikslus.", "featuredProject": "Išskirtinis projektas: Qochi Services", "projectTitle": "<PERSON><PERSON> padėjome Qochi paleisti švarią, šiuolaikišką repetitorių platformą", "testimonial1Quote": "UpZera padarė visą procesą sklandų. Svetainė yra greita, g<PERSON><PERSON><PERSON>, ir mūsų studentai m<PERSON>, kaip lengva rezervuoti sesijas.", "testimonial1Author": "— Qochi Services įkūrėjas", "checkItOut": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> →", "challengeTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "challengeDescription": "Qochi re<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kuri pristatytų jų repetitorių paslaugas — su galimybe studentams lengvai rezervuoti sesijas internetu ir bendrauti su prekės ženklu.", "deliveredTitle": "<PERSON><PERSON> prist<PERSON>", "delivered1Title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON> kra<PERSON> s<PERSON>", "delivered1Description": "Su elegant<PERSON>, profesijonaliu išdėstymu", "delivered2Title": "Rezervavimo integracija naudojan<PERSON>", "delivered2Description": "Leidžianti klientams be vargo rezervuoti repetitorių sesijas", "delivered3Title": "Apgalvotas turinio iš<PERSON>", "delivered3Description": "Orientuotas į pasitikėjimą, aiškumą ir konversiją", "delivered4Title": "<PERSON><PERSON><PERSON>, elegantiš<PERSON> dizaino sistema", "delivered4Description": "<PERSON><PERSON> atspindi švietimo pobūdį Qochi prekės ženkle", "techStackTitle": "Technologijų rinkinys", "resultsTitle": "Rezultatai", "result1": "Paleista per kelias dienas", "result2Title": "Visiškai pritaikyta mobiliesiems", "result2Description": "Tobuli balai mobiliojo naudo<PERSON>mo testuose", "result3": "Sklandus rezervavimo patyrimas", "result4": "Savininkas gali viską valdyti lengvai", "clientQuoteTitle": "<PERSON><PERSON>", "clientQuote": "UpZera komanda padarė tai be pastangų. <PERSON><PERSON> t<PERSON><PERSON> su<PERSON>, ko man re<PERSON>, ir pristat<PERSON> dar geriau nei įsivaizdavau.", "ctaTitle": "Norite tapti mūsų kita sėkmės istorija?", "ctaDescription": "Paverskim jūsų idėją gyvu produktu — greitai, individualiai ir visiškai paruoštu.", "ctaButton": "Rezervuokite nemokamą konsultaciją dabar!"}, "EmailTemplates": {"contactForm": {"notificationSubject": "Na<PERSON>ja kontaktų formos užklausa!! - {service}", "notificationTitle": "Nauja kontaktų formos užklausa", "notificationDescription": "Gauta nauja užklausa iš jūsų svetainės kontaktų formos.", "contactDetails": "Kontaktinė informacija", "name": "Vardas", "email": "El. <PERSON>", "service": "Paslauga", "message": "Žinutė", "confirmationSubject": "<PERSON><PERSON><PERSON><PERSON>, kad susis<PERSON> su UpZera!", "confirmationTitle": "Ačiū už jūsų užklausą!", "confirmationGreeting": "<PERSON><PERSON><PERSON>, {name}!", "confirmationMessage": "Gavome jūsų žinutę ir susisieksime su jumis per 24 valandas. Mūsų komanda peržiūrės jūsų užklausą ir pateiks išsamų atsakymą.", "confirmationNextSteps": "<PERSON><PERSON>?", "confirmationStep1": "Mūsų komanda peržiūrės jūsų užkla<PERSON>ą", "confirmationStep2": "Susisieksime su jumis per 24 valandas", "confirmationStep3": "Aptarsime jūsų projektą ir poreikius", "immediateAssistance": "<PERSON><PERSON><PERSON> skubio<PERSON> paga<PERSON>?", "contactUsAt": "Susisiekite su mumis", "bestRegards": "Geriausių linkėjimų,", "teamSignature": "UpZera komanda"}, "newsletter": {"notificationSubject": "Nauja prenumeratos užklausa", "notificationTitle": "Nauja prenumeratos užklausa", "notificationDescription": "Naujas prenumeratorius užsiregistravo jūsų naujienlaiškiui.", "subscriberEmail": "Prenumeratoriaus el. paš<PERSON>", "confirmationSubject": "Sveiki atvykę į UpZera naujienlaiškį!", "confirmationTitle": "Sėkmingai užsiregistravote!", "confirmationMessage": "<PERSON><PERSON><PERSON><PERSON>, kad prisijungėte prie UpZera bendruomenės! Gausite naujausias naujienas apie mūsų paslaugas, technologijų tendencijas ir ekskluzyvius pasiūlymus.", "whatToExpect": "<PERSON>:", "expectation1": "Savaitiniai technologijų atnaujinimai", "expectation2": "Eksklu<PERSON><PERSON><PERSON><PERSON> p<PERSON> ir nuo<PERSON>", "expectation3": "Patarimai ir geriausios prak<PERSON>kos", "expectation4": "Ankstyvasis prieigos prie naujų funkcijų", "unsubscribeNote": "Galite bet kada atsisakyti prenumeratos paspausdami nuorodą mūsų el. laiškų apačioje."}, "supportTicket": {"notificationSubject": "🎫 Naujas palaikymo bilietas #{ticketNumber} - {name}", "notificationTitle": "Naujas palaikymo bi<PERSON>", "notificationDescription": "Gautas naujas palaikymo bilietas iš kliento.", "ticketNumber": "Bilieto numeris", "customerInfo": "Kliento informacija", "problemDescription": "<PERSON><PERSON>", "conversationHistory": "Pokalbio istorija", "noConversationHistory": "Pokalbio istorija nepateikta", "confirmationSubject": "Palaikymo bilietas sukurtas - #{ticketNumber}", "confirmationTitle": "Jūsų palaikymo bilietas sukurtas!", "confirmationGreeting": "<PERSON><PERSON><PERSON>, {name}!", "confirmationMessage": "Sėkmingai sukūrėme jūsų palaikymo bilietą. Mūsų komanda peržiūrės jūsų problemą ir susisieks su jumis kuo greičiau.", "ticketDetails": "Bilieto informacija", "yourTicketNumber": "Jūsų bilieto numeris", "status": "<PERSON><PERSON><PERSON><PERSON>", "statusOpen": "<PERSON><PERSON><PERSON>", "nextSteps": "Tolimesni žingsniai", "nextStep1": "Mūsų palaikymo komanda peržiūrės jūs<PERSON>ą", "nextStep2": "Susisieksime su jumis per 24 valandas", "nextStep3": "<PERSON><PERSON><PERSON><PERSON> kart<PERSON>, kad išspręstume problemą", "trackTicket": "Sekti bi<PERSON>tą", "trackTicketDescription": "Išsaugokite šį bilieto numerį ateities nuorodoms"}, "common": {"copyrightText": "© {year} UpZera. Visos teisės saugomos.", "logoAlt": "UpZera logotipas"}}}